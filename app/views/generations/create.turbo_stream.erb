<%# Only render placeholders if generation task was created successfully %>
<% if @generation_task.present? %>
  <%# Prepend the first generation task placeholder %>
  <%= turbo_stream.prepend "songs_list" do %>
    <% 2.times do |index| %>
      <%= render "generations/placeholder", task: @generation_task, song_index: index %>
    <% end %>
  <% end %>
  <%= turbo_stream.replace "song_stats" do %>
    <%= render "songs/song_stats" %>
  <% end %>
  <%= turbo_stream.replace "credit_usage", method: :morph do %>
    <%= render "shared/credit_usage" %>
  <% end %>
<% end %>
<%= render_turbo_stream_flash_messages %>