<%= turbo_frame_tag "random-prompts-content", class: "block" do %>
  <div class="mt-3 space-y-2">
    <div class="flex items-center justify-between mb-2">
      <p class="text-sm text-gray-600">Click on a prompt to use it:</p>
      <%= link_to random_prompts_generations_path,
                  data: { 
                    turbo_frame: "random-prompts-content",
                    controller: "generation-form",
                    action: "click->generation-form#refreshPrompts"
                  },
                  class: "text-gray-500 hover:text-gray-700 transition-colors" do %>
        <%= flowbite_icon('refresh-outline', class: 'size-4') %>
      <% end %>
    </div>
    <div class="space-y-2">
      <% prompts.each do |prompt| %>
        <button type="button"
                data-action="click->generation-form#selectPrompt"
          data-prompt="<%= prompt %>"
          class="block w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors text-sm border border-gray-200 hover:border-gray-300 text-gray-900">
          <%= prompt %>
        </button>
      <% end %>
    </div>
  </div>
<% end %> 