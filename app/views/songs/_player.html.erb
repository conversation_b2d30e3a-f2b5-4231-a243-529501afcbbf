<!-- Persistent/Floating Media Player -->
<% if local_assigns[:song] && song.persisted? %>
  <div class="fixed bottom-0 left-0 right-0 md:left-64 z-20 transition-all duration-300 ease-in-out data-hidden:hidden" 
       data-controller="player" >
    <!-- Main player container -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <!-- Player component with external close button -->
      <div class="flex items-center gap-3">
        <%= turbo_frame_tag song_player_id(local_assigns[:song] || Song.new), class: "mt-2 contains-player flex-1" do %>
          <div class="w-full bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-purple-200/30 dark:border-gray-700 overflow-hidden relative">
            <media-player id="player" class="group/media w-full" autoplay
              data-player-target="player"
                      <% if local_assigns[:song] %>
                        stream-type=<%= song.has_final_audio? ? "on-demand" : "live" %>
                        view-type="audio"
                      <% end %>
          >
              <media-provider class="!contents">
                <% if local_assigns[:song] %>
                  <source src="<%= song.has_final_audio? ? song.audio_url : song.stream_audio_url %>" type="audio/mpeg" />
                <% end %>
              </media-provider>
              <!-- Controls Container -->
              <media-controls class="w-full">
                <!-- Top Progress Bar -->
                <media-controls-group class="flex w-full items-center px-3">
                  <!-- Time Slider -->
                  <media-time-slider
              class="group relative mx-[7.5px] inline-flex h-8 w-full cursor-pointer touch-none select-none items-center outline-none">
                    <media-slider-chapters class="relative flex h-full w-full items-center rounded-[1px]">
                      <template>
                        <!-- Slider Chapter -->
                        <div class="last-child:mr-0 relative mr-0.5 flex h-full w-full items-center rounded-[1px]"
                    style="contain: layout style">
                          <!-- Slider Chapter Track -->
                          <div
                      class="ring-purple-300 dark:ring-purple-600 relative z-0 h-[4px] w-full rounded-sm bg-gray-200 dark:bg-gray-600 group-data-[focus]:ring-[3px]">
                            <!-- Current Progress -->
                            <div class="bg-purple-600 z-10 absolute h-full w-(--chapter-fill) rounded-sm will-change-[width]">
                            </div>
                            <!-- Buffer Progress -->
                            <div
                        class="absolute h-full w-(--chapter-progress) rounded-sm bg-gray-300 dark:bg-gray-500 will-change-[width]">
                            </div>
                          </div>
                        </div>
                      </template>
                    </media-slider-chapters>
                    <!-- Slider Thumb -->
                    <div
                class="absolute left-(--slider-fill) top-1/2 z-20 h-[12px] w-[12px] -translate-x-1/2 -translate-y-1/2 rounded-full border border-purple-400 dark:border-purple-300 bg-white dark:bg-gray-200 opacity-0 ring-purple-200/60 dark:ring-purple-600/60 transition-opacity will-change-[left] group-data-[active]:opacity-100 group-data-[dragging]:ring-4 shadow-lg">
                    </div>
                  </media-time-slider>
                </media-controls-group>
                <!-- Main Player Container with Grid Layout - More Compact -->
                <media-controls-group class="grid grid-cols-3 gap-3 items-center px-4 pb-3">
                  <!-- 1. Left Section: Cover Art + Song Info -->
                  <div class="flex items-center space-x-3 min-w-0">
                    <div class="flex-shrink-0">
                      <div id="player__cover-art" class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-lg overflow-hidden shadow-sm">
                        <% if local_assigns[:song] && song.image_url.present? %>
                          <img src="<%= song.image_url %>" alt="<%= song.title %> cover" class="w-full h-full object-cover">
                        <% else %>
                          <div class="w-full h-full bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 flex items-center justify-center">
                            <%= flowbite_icon('music-outline', class: 'size-6 text-white') %>
                          </div>
                        <% end %>
                      </div>
                    </div>
                    <div class="min-w-0 max-w-xs">
                      <p class="text-sm font-semibold text-gray-900 dark:text-white truncate block">
                        <%= turbo_frame_tag dom_id(local_assigns[:song] || Song.new, :player_title), class: "contents" do %>
                          <%= local_assigns[:song] ? song.title : "Start playing a song" %>
                        <% end %>
                      </p>
                      <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 font-mono media-live:hidden">
                        <media-time type="current" class="tabular-nums"></media-time>
                        <span class="mx-1">/</span>
                        <media-time type="duration" class="tabular-nums"></media-time>
                      </div>
                      <div class="hidden items-center text-xs text-gray-500 dark:text-gray-400 font-mono media-live:flex">
                        LIVE
                      </div>
                    </div>
                  </div>
                  <!-- 2. Center: Play Button -->
                  <div class="flex justify-center">
                    <media-play-button class="not-media-can-play:cursor-not-allowed not-media-can-play:bg-gray-200 dark:not-media-can-play:bg-gray-600 group relative w-12 h-12 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-purple-200 dark:focus:ring-purple-800">
                      <%= flowbite_icon('play-solid', class: 'media-paused:block hidden size-6 text-white ml-0.5') %>
                      <%= flowbite_icon('pause-solid', class: 'media-paused:hidden size-6 text-white') %>
                    </media-play-button>
                  </div>
                  <!-- 3. Right Section: Action Buttons - More Compact -->
                  <div class="flex items-center space-x-1 justify-end">
                    <!-- Volume Control Button with Popover -->
                    <button 
                  data-popover-target="volume-popover" 
                  data-popover-placement="top"
                  data-popover-offset="4"
                  type="button"
                  class="p-1.5 rounded-lg hover:bg-purple-50 dark:hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 dark:focus:ring-purple-800" 
                  title="Volume">
                      <%= flowbite_icon('volume-mute-outline', class: "hidden size-4 text-gray-600 dark:text-gray-300 media-muted:block") %>
                      <%= flowbite_icon('volume-up-outline', class: "block size-4 text-gray-600 dark:text-gray-300 media-muted:hidden") %>
                    </button>
                    <!-- Volume Popover -->
                    <div data-popover id="volume-popover" role="tooltip" class="px-2 absolute z-10 invisible inline-block text-sm text-gray-500 dark:text-gray-400 transition-opacity duration-300 bg-white dark:bg-gray-700 border border-purple-200/30 dark:border-gray-600 rounded-lg shadow-xl opacity-0">
                      <media-volume-slider
                        class="group relative mx-2 inline-flex h-8 w-[100px] cursor-pointer touch-none select-none items-center outline-none">
                        <!-- Slider Track -->
                        <div class="ring-purple-300 dark:ring-purple-600 relative z-0 h-[4px] w-full rounded-sm bg-gray-200 dark:bg-gray-600 group-data-[focus]:ring-[3px]">
                          <!-- Current Volume -->
                          <div class="bg-purple-600 z-10 absolute h-full w-(--slider-fill) rounded-sm will-change-[width]"></div>
                        </div>
                        <!-- Slider Thumb -->
                        <div
                          class="absolute left-(--slider-fill) top-1/2 z-20 h-[12px] w-[12px] -translate-x-1/2 -translate-y-1/2 rounded-full border border-purple-400 dark:border-purple-300 bg-white dark:bg-gray-200 ring-purple-200/60 dark:ring-purple-600/60 will-change-[left] group-data-[dragging]:ring-4 shadow-lg">
                        </div>
                      </media-volume-slider>
                    </div>
                    <!-- Like Button -->
                    <%= render "songs/player_favorite_button", song: local_assigns[:song] %>
                    <!-- Download Button -->
                    <% if local_assigns[:song] %>
                      <% if song.has_final_audio? %>
                        <a href="<%= song.audio_url %>" target="_blank" download="<%= download_filename_for_song(song) %>"
                             class="p-1.5 rounded-lg hover:bg-purple-50 dark:hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 dark:focus:ring-purple-800" 
                             title="Download">
                          <%= flowbite_icon('download-outline', class: 'size-4 text-gray-600 dark:text-gray-300') %>
                        </a>
                      <% elsif song.has_stream_audio? %>
                        <button type="button" disabled
                                  class="cursor-not-allowed p-1.5 rounded-lg opacity-50 transition-colors"
                                  title="Final version not ready for download">
                          <%= flowbite_icon('download-outline', class: 'size-4 text-gray-400') %>
                        </button>
                      <% else %>
                        <button type="button" disabled
                                  class="cursor-not-allowed p-1.5 rounded-lg opacity-50 transition-colors"
                                  title="Song not ready for download">
                          <%= render "shared/loading_icon", class: 'size-4 text-gray-400 animate-spin' %>
                        </button>
                      <% end %>
                    <% else %>
                      <button class="cursor-not-allowed p-1.5 rounded-lg opacity-50 transition-colors" title="No song selected" disabled>
                        <%= flowbite_icon('download-outline', class: 'size-4 text-gray-400') %>
                      </button>
                    <% end %>
                  </div>
                </media-controls-group>
              </media-controls>
            </media-player>
          </div>
        <% end %>
        <!-- Close Button - Outside player -->
        <button 
        type="button"
        class="mt-2 p-2 rounded-full text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:focus:ring-gray-700 opacity-60 hover:opacity-100"
        data-action="click->player#hide"
          title="Close player"
          aria-label="Close player">
          <%= flowbite_icon('close-outline', class: 'size-4 text-current') %>
        </button>
      </div>
    </div>
  </div>
<% else %>
  <%= turbo_frame_tag "song_player", class: "mt-2" do %>
    <!-- Empty frame when no song is playing -->
  <% end %>
<% end %>